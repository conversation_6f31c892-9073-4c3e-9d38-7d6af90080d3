package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type User struct {
	ID       string `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username string `json:"username" gorm:"uniqueIndex;not null;size:50"`
	FullName string `json:"full_name" gorm:"not null;size:100"`
	Role     string `json:"role" gorm:"not null;default:'developer'"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询用户
	var user User
	err = db.Where("username = ?", "xiaochun").First(&user).Error
	if err != nil {
		log.Fatal("查询用户失败:", err)
	}

	fmt.Printf("用户信息:\n")
	fmt.Printf("ID: %s\n", user.ID)
	fmt.Printf("用户名: %s\n", user.Username)
	fmt.Printf("姓名: %s\n", user.FullName)
	fmt.Printf("角色: %s\n", user.Role)
}
