// 用户管理JavaScript功能
let currentEditingUser = null;
let allUsers = [];
let filteredUsers = [];
let currentPage = 1;
const usersPerPage = 10;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUserManagement();
    loadUsers();
    initializeAvatarUpload();
});

// 初始化用户管理功能
function initializeUserManagement() {
    const addUserBtn = document.getElementById('addUserBtn');
    const userModal = document.getElementById('userModal');
    const cancelUserBtn = document.getElementById('cancelUserBtn');
    const userForm = document.getElementById('userForm');
    const userSearch = document.getElementById('userSearch');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');

    // 新建用户按钮
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            openUserModal();
        });
    }

    // 取消按钮
    if (cancelUserBtn) {
        cancelUserBtn.addEventListener('click', function() {
            closeUserModal();
        });
    }

    // 点击背景关闭模态框
    if (userModal) {
        userModal.addEventListener('click', function(e) {
            if (e.target === userModal) {
                closeUserModal();
            }
        });
    }

    // 表单提交
    if (userForm) {
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleUserSubmit();
        });
    }

    // 密码确认实时验证
    const passwordField = document.getElementById('userPassword');
    const passwordConfirmField = document.getElementById('userPasswordConfirm');
    const passwordMatchMessage = document.getElementById('passwordMatchMessage');

    if (passwordConfirmField && passwordMatchMessage) {
        passwordConfirmField.addEventListener('input', function() {
            const password = passwordField.value;
            const passwordConfirm = passwordConfirmField.value;

            if (passwordConfirm.length > 0) {
                if (password === passwordConfirm) {
                    passwordMatchMessage.textContent = '✓ 密码匹配';
                    passwordMatchMessage.className = 'mt-1 text-xs text-green-600';
                    passwordMatchMessage.classList.remove('hidden');
                } else {
                    passwordMatchMessage.textContent = '✗ 密码不匹配';
                    passwordMatchMessage.className = 'mt-1 text-xs text-red-600';
                    passwordMatchMessage.classList.remove('hidden');
                }
            } else {
                passwordMatchMessage.classList.add('hidden');
            }
        });
    }

    // 搜索功能
    if (userSearch) {
        userSearch.addEventListener('input', function() {
            filterUsers();
        });
    }

    // 角色过滤
    if (roleFilter) {
        roleFilter.addEventListener('change', function() {
            filterUsers();
        });
    }

    // 状态过滤
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterUsers();
        });
    }
}

// 打开用户模态框
function openUserModal(user = null) {
    const modal = document.getElementById('userModal');
    const modalTitle = document.getElementById('userModalTitle');
    const form = document.getElementById('userForm');
    const passwordFields = document.getElementById('passwordFields');
    const passwordMatchMessage = document.getElementById('passwordMatchMessage');

    currentEditingUser = user;

    if (user) {
        // 编辑模式
        modalTitle.textContent = '编辑用户';
        document.getElementById('userName').value = user.username || '';
        document.getElementById('userEmail').value = user.email || '';
        document.getElementById('userFullName').value = user.full_name || '';
        document.getElementById('userRole').value = user.role || 'developer';
        document.getElementById('userAvatar').value = user.avatar || '';

        // 显示现有头像
        displayExistingAvatar(user.avatar);

        // 编辑模式下禁用用户名输入，但保留密码字段用于重置密码
        document.getElementById('userName').disabled = true;
        if (passwordFields) {
            passwordFields.style.display = 'block';
            // 清空密码字段，让管理员可以设置新密码
            document.getElementById('userPassword').value = '';
            document.getElementById('userPasswordConfirm').value = '';
            // 编辑模式下密码字段不是必填的（如果不填则不修改密码）
            document.getElementById('userPassword').required = false;
            document.getElementById('userPasswordConfirm').required = false;
            // 添加提示文本
            const passwordLabel = document.querySelector('label[for="userPassword"]');
            if (passwordLabel) {
                passwordLabel.innerHTML = '新密码 <span class="text-gray-500">(留空则不修改)</span>';
            }
        }
    } else {
        // 新建模式
        modalTitle.textContent = '新建用户';
        form.reset();
        document.getElementById('userName').disabled = false;

        // 新建模式下显示密码字段并设为必填
        if (passwordFields) {
            passwordFields.style.display = 'block';
            document.getElementById('userPassword').required = true;
            document.getElementById('userPasswordConfirm').required = true;
            // 恢复密码标签文本
            const passwordLabel = document.querySelector('label[for="userPassword"]');
            if (passwordLabel) {
                passwordLabel.innerHTML = '密码 <span class="text-red-500">*</span>';
            }
        }

        // 清除密码匹配提示
        if (passwordMatchMessage) {
            passwordMatchMessage.classList.add('hidden');
        }

        // 清除头像显示
        removeAvatar();
    }

    modal.classList.remove('hidden');
    document.getElementById('userName').focus();
}

// 关闭用户模态框
function closeUserModal() {
    const modal = document.getElementById('userModal');
    modal.classList.add('hidden');
    currentEditingUser = null;
}

// 处理用户提交
function handleUserSubmit() {
    const username = document.getElementById('userName').value.trim();
    const email = document.getElementById('userEmail').value.trim();
    const fullName = document.getElementById('userFullName').value.trim();
    const role = document.getElementById('userRole').value;
    const avatar = document.getElementById('userAvatar').value.trim();

    // 基本字段验证
    if (!username || !email || !fullName) {
        showToast('请填写所有必填字段', 'error');
        return;
    }

    const userData = {
        username: username,
        email: email,
        full_name: fullName,
        role: role,
        avatar: avatar
    };

    let url, method;
    if (currentEditingUser) {
        // 编辑用户
        url = `/api/users/${currentEditingUser.id}`;
        method = 'PUT';
        // 编辑时不发送用户名
        delete userData.username;

        // 检查是否要更新密码
        const password = document.getElementById('userPassword').value;
        const passwordConfirm = document.getElementById('userPasswordConfirm').value;

        if (password || passwordConfirm) {
            // 如果填写了密码，进行验证
            if (password.length < 6) {
                showToast('密码长度至少6位字符', 'error');
                return;
            }

            if (password !== passwordConfirm) {
                showToast('两次输入的密码不一致', 'error');
                return;
            }

            // 添加密码到用户数据
            userData.password = password;
        }
    } else {
        // 新建用户 - 需要密码验证
        const password = document.getElementById('userPassword').value;
        const passwordConfirm = document.getElementById('userPasswordConfirm').value;

        // 密码验证
        if (!password || !passwordConfirm) {
            showToast('请填写密码和确认密码', 'error');
            return;
        }

        if (password.length < 6) {
            showToast('密码长度至少6位字符', 'error');
            return;
        }

        if (password !== passwordConfirm) {
            showToast('两次输入的密码不一致', 'error');
            return;
        }

        // 添加密码到用户数据
        userData.password = password;

        url = '/api/auth/register';  // 使用注册API创建带密码的用户
        method = 'POST';
    }
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        // 处理不同API的响应格式
        if (currentEditingUser) {
            // 编辑用户使用原有API格式
            if (data.error) {
                showToast(data.error, 'error');
            } else {
                showToast('用户更新成功', 'success');
                closeUserModal();
                loadUsers();
            }
        } else {
            // 新建用户使用注册API格式
            if (data.success) {
                showToast(data.message || '用户创建成功', 'success');
                closeUserModal();
                loadUsers();
            } else {
                showToast(data.message || '创建用户失败', 'error');
            }
        }
    })
    .catch(error => {
        console.error('网络错误:', error);
        showToast('网络错误，请稍后重试', 'error');
    });
}

// 加载用户列表
function loadUsers() {
    fetch('/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.users) {
                allUsers = data.users;
                filterUsers();
                updateUserStats();
            }
        })
        .catch(error => {
            console.error('加载用户失败:', error);
            showToast('加载用户失败', 'error');
        });
}

// 过滤用户
function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredUsers = allUsers.filter(user => {
        const matchesSearch = !searchTerm || 
            user.username.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm) ||
            user.full_name.toLowerCase().includes(searchTerm);
        
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesStatus = !statusFilter || 
            (statusFilter === 'active' && user.is_active) ||
            (statusFilter === 'inactive' && !user.is_active);
        
        return matchesSearch && matchesRole && matchesStatus;
    });
    
    currentPage = 1;
    displayUsers();
    updatePagination();
}

// 显示用户列表
function displayUsers() {
    const tbody = document.getElementById('userTableBody');
    if (!tbody) return;
    
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const pageUsers = filteredUsers.slice(startIndex, endIndex);
    
    tbody.innerHTML = '';
    
    pageUsers.forEach(user => {
        const row = createUserRow(user);
        tbody.appendChild(row);
    });
    
    // 更新记录显示
    const totalRecords = filteredUsers.length;
    const startRecord = totalRecords > 0 ? startIndex + 1 : 0;
    const endRecord = Math.min(endIndex, totalRecords);
    
    document.getElementById('startRecord').textContent = startRecord;
    document.getElementById('endRecord').textContent = endRecord;
    document.getElementById('totalRecords').textContent = totalRecords;
}

// 创建用户行
function createUserRow(user) {
    const row = document.createElement('tr');
    row.className = 'hover:bg-gray-50';
    
    const roleDisplayName = getRoleDisplayName(user.role);
    const roleColorClass = getRoleColorClass(user.role);
    const statusClass = user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    const statusText = user.is_active ? '活跃' : '停用';
    
    row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10">
                    <img class="h-10 w-10 rounded-full object-cover" src="${user.avatar || getDefaultAvatar(user.full_name)}" alt="${user.full_name}" onerror="this.src=getDefaultAvatar('${user.full_name}')">
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">${user.full_name}</div>
                    <div class="text-sm text-gray-500">${user.username}</div>
                    <div class="text-sm text-gray-500">${user.email}</div>
                </div>
            </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleColorClass}">
                ${roleDisplayName}
            </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                ${statusText}
            </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            ${user.last_login_at ? formatDate(user.last_login_at) : '从未登录'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            ${formatDate(user.created_at)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <div class="flex space-x-2">
                <button onclick="editUser('${user.id}')" class="text-blue-600 hover:text-blue-900">编辑</button>
                <button onclick="toggleUserStatus('${user.id}', ${user.is_active})" class="text-${user.is_active ? 'red' : 'green'}-600 hover:text-${user.is_active ? 'red' : 'green'}-900">
                    ${user.is_active ? '停用' : '激活'}
                </button>
                <button onclick="deleteUser('${user.id}')" class="text-red-600 hover:text-red-900">删除</button>
            </div>
        </td>
    `;
    
    return row;
}

// 编辑用户
function editUser(userId) {
    const user = allUsers.find(u => u.id === userId);
    if (user) {
        openUserModal(user);
    }
}

// 切换用户状态
function toggleUserStatus(userId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const actionText = isActive ? '停用' : '激活';

    if (!confirm(`确定要${actionText}这个用户吗？`)) {
        return;
    }

    fetch(`/api/users/${userId}/${action}`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showToast(data.error, 'error');
        } else {
            showToast(`用户${actionText}成功`, 'success');
            loadUsers();
        }
    })
    .catch(error => {
        console.error('操作失败:', error);
        showToast('操作失败', 'error');
    });
}

// 删除用户
function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作不可恢复！')) {
        return;
    }

    fetch(`/api/users/${userId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showToast(data.error, 'error');
        } else {
            showToast('用户删除成功', 'success');
            loadUsers();
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showToast('删除失败', 'error');
    });
}

// 更新用户统计
function updateUserStats() {
    const totalUsers = allUsers.length;
    const activeUsers = allUsers.filter(u => u.is_active).length;
    const adminUsers = allUsers.filter(u => u.role === 'admin').length;
    const developerUsers = allUsers.filter(u => u.role === 'developer').length;

    document.getElementById('totalUsers').textContent = totalUsers;
    document.getElementById('activeUsers').textContent = activeUsers;
    document.getElementById('adminUsers').textContent = adminUsers;
    document.getElementById('developerUsers').textContent = developerUsers;
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const pagination = document.getElementById('pagination');

    if (!pagination) return;

    pagination.innerHTML = '';

    // 上一页按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}`;
    prevBtn.innerHTML = '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => {
        if (currentPage > 1) {
            currentPage--;
            displayUsers();
            updatePagination();
        }
    };
    pagination.appendChild(prevBtn);

    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${i === currentPage ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => {
                currentPage = i;
                displayUsers();
                updatePagination();
            };
            pagination.appendChild(pageBtn);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const dots = document.createElement('span');
            dots.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
            dots.textContent = '...';
            pagination.appendChild(dots);
        }
    }

    // 下一页按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}`;
    nextBtn.innerHTML = '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => {
        if (currentPage < totalPages) {
            currentPage++;
            displayUsers();
            updatePagination();
        }
    };
    pagination.appendChild(nextBtn);
}

// 获取角色显示名称
function getRoleDisplayName(role) {
    const roleNames = {
        'admin': '👑 管理员',
        'manager': '👨‍💼 项目经理',
        'developer': '🧑‍💻 开发者',
        'viewer': '👁️ 查看者'
    };
    return roleNames[role] || '🧑‍💻 开发者';
}

// 获取角色颜色类
function getRoleColorClass(role) {
    const roleColors = {
        'admin': 'text-red-600 bg-red-100',
        'manager': 'text-blue-600 bg-blue-100',
        'developer': 'text-green-600 bg-green-100',
        'viewer': 'text-gray-600 bg-gray-100'
    };
    return roleColors[role] || 'text-gray-600 bg-gray-100';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 显示提示消息 - 用户页面专用版本
function showToast(message, type = 'info') {
    // 为了避免任何递归问题，用户页面使用独立的toast系统
    createSimpleToast(message, type);
}

// 创建简单的toast通知（备用方案）
function createSimpleToast(message, type = 'info') {
    // 创建通知容器（如果不存在）
    let container = document.getElementById('simple-toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'simple-toast-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 12px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }

    // 创建通知元素
    const toast = document.createElement('div');
    const toastId = 'simple-toast-' + Date.now();
    toast.id = toastId;

    // 根据类型设置样式
    const colors = {
        success: 'background: #10b981; color: white;',
        error: 'background: #ef4444; color: white;',
        warning: 'background: #f59e0b; color: white;',
        info: 'background: #3b82f6; color: white;'
    };

    toast.style.cssText = `
        ${colors[type] || colors.info}
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        font-weight: 500;
        max-width: 350px;
        pointer-events: auto;
        cursor: pointer;
        transition: all 0.3s ease;
        transform: translateX(100%);
        opacity: 0;
    `;

    toast.textContent = message;

    // 点击关闭
    toast.onclick = function() {
        hideSimpleToast(toastId);
    };

    container.appendChild(toast);

    // 动画显示
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        hideSimpleToast(toastId);
    }, 3000);
}

// 隐藏简单toast
function hideSimpleToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// 生成默认头像
function getDefaultAvatar(fullName) {
    // 获取姓名的首字母
    const initials = fullName ? fullName.charAt(0).toUpperCase() : '?';

    // 生成颜色（基于姓名哈希）
    let hash = 0;
    for (let i = 0; i < fullName.length; i++) {
        hash = fullName.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
        '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
        '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ];

    const color = colors[Math.abs(hash) % colors.length];

    // 创建SVG头像
    const svg = `data:image/svg+xml,${encodeURIComponent(`
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
            <rect width="40" height="40" fill="${color}" rx="20"/>
            <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">${initials}</text>
        </svg>
    `)}`;

    return svg;
}

// ==================== 头像上传功能 ====================

// 初始化头像上传功能
function initializeAvatarUpload() {
    const avatarFile = document.getElementById('avatarFile');
    if (avatarFile) {
        avatarFile.addEventListener('change', handleAvatarFileSelect);
    }
}

// 选择头像文件
function selectAvatarFile() {
    const avatarFile = document.getElementById('avatarFile');
    if (avatarFile) {
        avatarFile.click();
    }
}

// 处理头像文件选择
function handleAvatarFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
    if (!allowedTypes.includes(file.type)) {
        showToast('只支持 JPG、PNG、GIF、BMP 格式的图片', 'error');
        return;
    }

    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        showToast('头像文件大小不能超过5MB', 'error');
        return;
    }

    // 显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarPlaceholder = document.getElementById('avatarPlaceholder');
        const removeBtn = document.getElementById('removeAvatarBtn');

        if (avatarPreview && avatarPlaceholder) {
            avatarPreview.src = e.target.result;
            avatarPreview.style.display = 'block';
            avatarPlaceholder.style.display = 'none';
            if (removeBtn) removeBtn.style.display = 'inline-block';
        }
    };
    reader.readAsDataURL(file);

    // 上传头像
    uploadAvatar(file);
}

// 上传头像到服务器
function uploadAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);

    // 显示上传进度提示
    showToast('正在上传头像...', 'info');

    fetch('/api/users/avatar/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showToast(data.error, 'error');
            removeAvatar(); // 上传失败时清除预览
        } else {
            showToast(data.message, 'success');
            // 将头像URL保存到隐藏字段
            const avatarInput = document.getElementById('userAvatar');
            if (avatarInput) {
                avatarInput.value = data.avatar_url;
            }
        }
    })
    .catch(error => {
        console.error('头像上传失败:', error);
        showToast('头像上传失败', 'error');
        removeAvatar(); // 上传失败时清除预览
    });
}

// 删除头像
function removeAvatar() {
    const avatarPreview = document.getElementById('avatarPreview');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const avatarInput = document.getElementById('userAvatar');
    const avatarFile = document.getElementById('avatarFile');
    const removeBtn = document.getElementById('removeAvatarBtn');

    if (avatarPreview) {
        avatarPreview.src = '';
        avatarPreview.style.display = 'none';
    }
    if (avatarPlaceholder) {
        avatarPlaceholder.style.display = 'flex';
    }
    if (avatarInput) {
        avatarInput.value = '';
    }
    if (avatarFile) {
        avatarFile.value = '';
    }
    if (removeBtn) {
        removeBtn.style.display = 'none';
    }
}

// 显示现有头像
function displayExistingAvatar(avatarUrl) {
    const avatarPreview = document.getElementById('avatarPreview');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const removeBtn = document.getElementById('removeAvatarBtn');

    if (avatarUrl && avatarPreview && avatarPlaceholder) {
        avatarPreview.src = avatarUrl;
        avatarPreview.style.display = 'block';
        avatarPlaceholder.style.display = 'none';
        if (removeBtn) removeBtn.style.display = 'inline-block';
    } else {
        // 没有头像时显示占位符
        if (avatarPreview) avatarPreview.style.display = 'none';
        if (avatarPlaceholder) avatarPlaceholder.style.display = 'flex';
        if (removeBtn) removeBtn.style.display = 'none';
    }
}
