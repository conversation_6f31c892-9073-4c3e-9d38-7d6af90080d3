package database

import (
	"crypto/sha256"
	"fmt"
	"projectm2/internal/models"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDB 初始化数据库连接
func InitDB() (*gorm.DB, error) {
	// 连接SQLite数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 生产环境关闭SQL日志
	})
	if err != nil {
		return nil, err
	}

	// 执行数据库迁移
	err = performMigration(db)
	if err != nil {
		return nil, err
	}

	// 添加性能优化索引
	if err := addPerformanceIndexes(db); err != nil {
		return nil, err
	}

	// 初始化示例数据
	if err := seedData(db); err != nil {
		return nil, err
	}

	// 初始化默认任务类型
	if err := initializeDefaultTaskTypes(db); err != nil {
		return nil, err
	}

	// 初始化默认任务模板
	if err := initializeDefaultTaskTemplates(db); err != nil {
		return nil, err
	}

	return db, nil
}

// performMigration 执行数据库迁移
func performMigration(db *gorm.DB) error {
	// 先尝试正常的自动迁移
	err := db.AutoMigrate(&models.Task{}, &models.Comment{}, &models.Attachment{}, &models.TaskTypeModel{}, &models.TaskVersion{}, &models.TaskTemplate{})
	if err != nil {
		return err
	}

	// 检查并添加用户表的新字段
	err = migrateUserTable(db)
	if err != nil {
		return err
	}

	// 最后迁移用户表
	err = db.AutoMigrate(&models.User{})
	if err != nil {
		return err
	}

	return nil
}

// migrateUserTable 迁移用户表
func migrateUserTable(db *gorm.DB) error {
	// 检查密码字段
	if !db.Migrator().HasColumn(&models.User{}, "password") {
		err := db.Exec("ALTER TABLE users ADD COLUMN password TEXT DEFAULT ''").Error
		if err != nil {
			return err
		}

		// 为现有用户设置默认密码
		err = db.Exec("UPDATE users SET password = ? WHERE password = '' OR password IS NULL", hashPassword("admin123")).Error
		if err != nil {
			return err
		}
	}

	// 检查登录次数字段
	if !db.Migrator().HasColumn(&models.User{}, "login_count") {
		err := db.Exec("ALTER TABLE users ADD COLUMN login_count INTEGER DEFAULT 0").Error
		if err != nil {
			return err
		}
	}

	// 确保现有用户有密码
	var count int64
	db.Model(&models.User{}).Where("password = '' OR password IS NULL").Count(&count)
	if count > 0 {
		err := db.Exec("UPDATE users SET password = ? WHERE password = '' OR password IS NULL", hashPassword("admin123")).Error
		if err != nil {
			return err
		}
	}

	return nil
}

// addPerformanceIndexes 添加性能优化索引
func addPerformanceIndexes(db *gorm.DB) error {
	// 为Task表添加复合索引，优化常用查询
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tasks_status_position ON tasks(status, position)").Error; err != nil {
		return err
	}

	// 为Task表添加创建时间索引，优化时间范围查询
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at)").Error; err != nil {
		return err
	}

	// 为Task表添加更新时间索引，优化最近活动查询
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tasks_updated_at ON tasks(updated_at)").Error; err != nil {
		return err
	}

	// 为User表添加活跃状态索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)").Error; err != nil {
		return err
	}

	return nil
}

// seedData 初始化示例数据
func seedData(db *gorm.DB) error {
	// 首先确保用户数据存在
	if err := ensureDefaultUsers(db); err != nil {
		return err
	}

	// 检查是否已有任务数据
	var taskCount int64
	db.Model(&models.Task{}).Count(&taskCount)
	if taskCount > 0 {
		// 检查现有任务是否有完整的日期数据
		var tasksWithDates int64
		db.Model(&models.Task{}).Where("start_date IS NOT NULL OR end_date IS NOT NULL").Count(&tasksWithDates)

		// 强制更新现有任务数据（临时修复）
		fmt.Printf("发现 %d 个任务，其中 %d 个有日期数据\n", taskCount, tasksWithDates)

		// 检查是否有任务缺少日期数据
		var tasksWithoutDates int64
		db.Model(&models.Task{}).Where("start_date IS NULL OR start_date = ''").Count(&tasksWithoutDates)
		fmt.Printf("缺少日期数据的任务数量: %d\n", tasksWithoutDates)

		if tasksWithoutDates > 0 {
			fmt.Println("使用SQL直接更新现有任务的示例数据...")

			// 直接使用SQL更新，避免GORM的复杂性
			result := db.Exec(`
				UPDATE tasks
				SET start_date = '2025-07-07T10:00:00Z',
				    end_date = '2025-07-14T10:00:00Z',
				    due_date = '2025-07-14T10:00:00Z',
				    estimated_hours = 8,
				    actual_hours = 2,
				    assigned_to = 'default-user'
				WHERE start_date IS NULL OR start_date = ''
			`)

			if result.Error != nil {
				fmt.Printf("SQL更新失败: %v\n", result.Error)
				return result.Error
			}

			fmt.Printf("SQL更新成功，影响行数: %d\n", result.RowsAffected)
		}
		return nil
	}

	// 获取创建的用户ID用于任务分配
	var adminUser, managerUser, dev1User models.User
	db.Where("username = ?", "admin").First(&adminUser)
	db.Where("username = ?", "manager").First(&managerUser)
	db.Where("username = ?", "developer1").First(&dev1User)

	// 创建示例任务（包含完整的日期和工时数据）
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	nextWeek := now.AddDate(0, 0, 7)
	yesterday := now.AddDate(0, 0, -1)

	sampleTasks := []models.Task{
		{
			Title:          "设计用户界面",
			Description:    "创建现代化的看板界面设计，确保用户体验流畅",
			Status:         models.StatusTodo,
			Priority:       models.PriorityHigh,
			Position:       0,
			AssignedTo:     &dev1User.ID,
			CreatedBy:      &managerUser.ID,
			StartDate:      &now,
			EndDate:        &nextWeek,
			DueDate:        &nextWeek,
			EstimatedHours: 16,
			ActualHours:    0,
		},
		{
			Title:          "实现拖拽功能",
			Description:    "集成SortableJS实现任务卡片的拖拽排序功能",
			Status:         models.StatusTodo,
			Priority:       models.PriorityMedium,
			Position:       1,
			AssignedTo:     &dev1User.ID,
			CreatedBy:      &managerUser.ID,
			StartDate:      &tomorrow,
			EndDate:        &nextWeek,
			DueDate:        &nextWeek,
			EstimatedHours: 8,
			ActualHours:    0,
		},
		{
			Title:          "搭建后端API",
			Description:    "使用Gin框架构建RESTful API接口",
			Status:         models.StatusInProgress,
			Priority:       models.PriorityHigh,
			Position:       0,
			AssignedTo:     &dev1User.ID,
			CreatedBy:      &adminUser.ID,
			StartDate:      &yesterday,
			EndDate:        &nextWeek,
			DueDate:        &nextWeek,
			EstimatedHours: 24,
			ActualHours:    12,
		},
		{
			Title:          "数据库设计",
			Description:    "设计SQLite数据库表结构，定义任务模型",
			Status:         models.StatusDone,
			Priority:       models.PriorityMedium,
			Position:       0,
			AssignedTo:     &dev1User.ID,
			CreatedBy:      &adminUser.ID,
			StartDate:      &yesterday,
			EndDate:        &now,
			DueDate:        &now,
			EstimatedHours: 6,
			ActualHours:    8,
		},
		{
			Title:          "项目初始化",
			Description:    "初始化Go项目，安装必要的依赖包",
			Status:         models.StatusDone,
			Priority:       models.PriorityLow,
			Position:       1,
			CreatedBy:      &adminUser.ID,
			StartDate:      &yesterday,
			EndDate:        &yesterday,
			DueDate:        &yesterday,
			EstimatedHours: 2,
			ActualHours:    3,
		},
	}

	// 批量创建示例任务
	for i, task := range sampleTasks {
		// 为每个任务设置一些示例标签
		switch i {
		case 0: // 设计用户界面
			task.SetTags([]string{"UI", "设计", "前端"})
		case 1: // 实现拖拽功能
			task.SetTags([]string{"JavaScript", "交互", "前端"})
		case 2: // 搭建后端API
			task.SetTags([]string{"Go", "API", "后端"})
		case 3: // 数据库设计
			task.SetTags([]string{"数据库", "SQLite", "设计"})
		case 4: // 项目初始化
			task.SetTags([]string{"初始化", "配置", "环境"})
		}

		if err := db.Create(&task).Error; err != nil {
			return err
		}
	}

	return nil
}

// updateExistingTasksWithSampleData 更新现有任务的示例数据
func updateExistingTasksWithSampleData(db *gorm.DB) error {
	fmt.Println("开始更新现有任务的示例数据...")

	// 获取所有现有任务
	var tasks []models.Task
	if err := db.Find(&tasks).Error; err != nil {
		fmt.Printf("获取任务失败: %v\n", err)
		return err
	}

	fmt.Printf("找到 %d 个任务需要更新\n", len(tasks))

	// 获取用户ID用于分配
	var adminUser, managerUser, dev1User models.User
	db.Where("username = ?", "admin").First(&adminUser)
	db.Where("username = ?", "manager").First(&managerUser)
	db.Where("username = ?", "developer1").First(&dev1User)

	// 为现有任务添加示例数据
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	nextWeek := now.AddDate(0, 0, 7)
	yesterday := now.AddDate(0, 0, -1)

	for i, task := range tasks {
		fmt.Printf("更新任务 %d: %s\n", i+1, task.Title)
		// 根据任务索引设置不同的示例数据
		switch i % 5 {
		case 0:
			task.StartDate = &now
			task.EndDate = &nextWeek
			task.DueDate = &nextWeek
			task.EstimatedHours = 16
			task.ActualHours = 0
			task.AssignedTo = &dev1User.ID
			task.SetTags([]string{"UI", "设计", "前端"})
		case 1:
			task.StartDate = &tomorrow
			task.EndDate = &nextWeek
			task.DueDate = &nextWeek
			task.EstimatedHours = 8
			task.ActualHours = 0
			task.AssignedTo = &dev1User.ID
			task.SetTags([]string{"JavaScript", "交互", "前端"})
		case 2:
			task.StartDate = &yesterday
			task.EndDate = &nextWeek
			task.DueDate = &nextWeek
			task.EstimatedHours = 24
			task.ActualHours = 12
			task.AssignedTo = &dev1User.ID
			task.SetTags([]string{"Go", "API", "后端"})
		case 3:
			task.StartDate = &yesterday
			task.EndDate = &now
			task.DueDate = &now
			task.EstimatedHours = 6
			task.ActualHours = 8
			task.AssignedTo = &dev1User.ID
			task.SetTags([]string{"数据库", "SQLite", "设计"})
		case 4:
			task.StartDate = &yesterday
			task.EndDate = &yesterday
			task.DueDate = &yesterday
			task.EstimatedHours = 2
			task.ActualHours = 3
			task.AssignedTo = &adminUser.ID
			task.SetTags([]string{"初始化", "配置", "环境"})
		}

		// 保存更新的任务
		if err := db.Save(&task).Error; err != nil {
			fmt.Printf("保存任务失败: %v\n", err)
			return err
		}
		fmt.Printf("任务 %s 更新成功\n", task.Title)
	}

	fmt.Println("所有任务更新完成")
	return nil
}

// initializeDefaultTaskTypes 初始化默认任务类型
func initializeDefaultTaskTypes(db *gorm.DB) error {
	// 检查是否已经初始化过
	var count int64
	db.Model(&models.TaskTypeModel{}).Count(&count)
	if count > 0 {
		return nil // 已经有数据，跳过初始化
	}

	// 插入默认类型
	defaultTypes := models.GetDefaultTaskTypes()
	for _, taskType := range defaultTypes {
		if err := db.Create(&taskType).Error; err != nil {
			return err
		}
	}

	return nil
}

// ensureDefaultUsers 确保默认用户存在
func ensureDefaultUsers(db *gorm.DB) error {
	// 检查admin用户是否存在
	var adminUser models.User
	err := db.Where("username = ?", "admin").First(&adminUser).Error
	if err == nil {
		return nil // admin用户已存在，跳过初始化
	}

	// 创建示例用户
	sampleUsers := []models.User{
		{
			ID:       "default-user", // 设置固定ID以匹配API返回
			Username: "admin",
			Email:    "<EMAIL>",
			Password: hashPassword("admin123"), // 默认密码：admin123
			FullName: "系统管理员",
			Role:     models.RoleAdmin,
			IsActive: true,
		},
		{
			Username: "manager",
			Email:    "<EMAIL>",
			Password: hashPassword("manager123"), // 默认密码：manager123
			FullName: "项目经理",
			Role:     models.RoleManager,
			IsActive: true,
		},
		{
			Username: "developer1",
			Email:    "<EMAIL>",
			Password: hashPassword("dev123"), // 默认密码：dev123
			FullName: "张三",
			Role:     models.RoleDeveloper,
			IsActive: true,
		},
		{
			Username: "developer2",
			Email:    "<EMAIL>",
			Password: hashPassword("dev123"), // 默认密码：dev123
			FullName: "李四",
			Role:     models.RoleDeveloper,
			IsActive: true,
		},
	}

	// 批量创建示例用户
	for _, user := range sampleUsers {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	return nil
}

// initializeDefaultTaskTemplates 初始化默认任务模板
func initializeDefaultTaskTemplates(db *gorm.DB) error {
	// 检查是否已经有模板数据
	var count int64
	db.Model(&models.TaskTemplate{}).Count(&count)
	if count > 0 {
		return nil // 已有数据，跳过初始化
	}

	// 获取管理员用户ID作为模板创建者
	var adminUser models.User
	err := db.Where("role = ?", models.RoleAdmin).First(&adminUser).Error
	if err != nil {
		return err
	}

	// 默认模板数据 - 为每个任务类型创建对应模板
	defaultTemplates := []models.TaskTemplate{
		{
			Name:        "🐛 Bug修复",
			Description: "用于修复系统缺陷和错误",
			Title:       "修复：[描述问题]",
			Content: `## 问题描述
[详细描述遇到的问题]

## 重现步骤
1.
2.
3.

## 预期结果
[描述预期的正确行为]

## 实际结果
[描述实际发生的错误行为]

## 解决方案
[描述修复方案]

## 测试验证
- [ ] 功能测试
- [ ] 回归测试`,
			Priority:  models.PriorityHigh,
			Type:      models.TypeBug,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 1,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "🚀 功能开发",
			Description: "用于新功能的开发和实现",
			Title:       "功能：[功能名称]",
			Content: `## 功能描述
[详细描述要开发的功能]

## 需求分析
[分析功能需求和用户场景]

## 技术方案
[描述技术实现方案]

## 开发计划
- [ ] 需求分析
- [ ] 技术设计
- [ ] 编码实现
- [ ] 单元测试
- [ ] 集成测试
- [ ] 文档更新

## 验收标准
[定义功能完成的验收标准]`,
			Priority:  models.PriorityMedium,
			Type:      models.TypeFeature,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 2,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "📋 一般任务",
			Description: "用于普通的工作任务",
			Title:       "任务：[任务名称]",
			Content: `## 任务描述
[详细描述要完成的任务]

## 任务目标
[明确任务要达到的目标]

## 执行计划
- [ ] 步骤1
- [ ] 步骤2
- [ ] 步骤3

## 注意事项
[列出需要注意的重要事项]

## 完成标准
[定义任务完成的标准]`,
			Priority:  models.PriorityMedium,
			Type:      models.TypeTask,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 3,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "⚡ 改进优化",
			Description: "用于性能优化和改进工作",
			Title:       "优化：[优化内容]",
			Content: `## 优化目标
[描述要优化的性能指标或改进目标]

## 现状分析
### 当前性能
- 响应时间：
- 吞吐量：
- 资源使用：

### 问题识别
[识别性能瓶颈或改进点]

## 优化方案
[描述具体的优化措施]

## 预期效果
[描述优化后的预期效果]

## 验证计划
- [ ] 性能测试
- [ ] 压力测试
- [ ] 监控验证`,
			Priority:  models.PriorityMedium,
			Type:      models.TypeImprovement,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 4,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "🔍 研究调研",
			Description: "用于技术调研和研究工作",
			Title:       "调研：[调研主题]",
			Content: `## 调研背景
[说明调研的背景和必要性]

## 调研目标
[明确调研要达到的目标]

## 调研范围
- 技术方案对比
- 最佳实践分析
- 风险评估

## 调研计划
- [ ] 资料收集
- [ ] 技术验证
- [ ] 方案对比
- [ ] 结论总结

## 预期产出
[描述调研的预期成果]`,
			Priority:  models.PriorityMedium,
			Type:      models.TypeResearch,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 5,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "🧪 测试任务",
			Description: "用于测试计划和测试执行",
			Title:       "测试：[测试内容]",
			Content: `## 测试目标
[描述测试的目标和范围]

## 测试环境
- 操作系统：
- 浏览器：
- 版本：

## 测试用例
### 用例1：[用例名称]
- **前置条件：**
- **操作步骤：**
  1.
  2.
  3.
- **预期结果：**

## 测试结果
- [ ] 通过
- [ ] 失败
- [ ] 阻塞

## 问题记录
[记录发现的问题]`,
			Priority:  models.PriorityMedium,
			Type:      models.TypeTesting,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 6,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "📚 文档编写",
			Description: "用于文档编写和维护",
			Title:       "文档：[文档标题]",
			Content: `## 文档目的
[说明文档的目的和用途]

## 目标读者
[描述文档的目标读者]

## 内容大纲
1. 概述
2. 详细说明
3. 示例
4. 注意事项

## 编写计划
- [ ] 收集资料
- [ ] 编写初稿
- [ ] 内容审核
- [ ] 发布更新

## 维护计划
[描述文档的维护和更新计划]`,
			Priority:  models.PriorityLow,
			Type:      models.TypeDocumentation,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 7,
			CreatedBy: &adminUser.ID,
		},
		{
			Name:        "🔧 维护任务",
			Description: "用于系统维护和运维工作",
			Title:       "维护：[维护内容]",
			Content: `## 维护目标
[描述维护的目标和范围]

## 维护内容
- [ ] 系统检查
- [ ] 数据备份
- [ ] 性能监控
- [ ] 安全检查

## 维护计划
### 定期维护
- 日常检查：
- 周期性维护：
- 紧急维护：

## 注意事项
[列出维护过程中需要注意的事项]

## 验收标准
[定义维护完成的标准]`,
			Priority:  models.PriorityLow,
			Type:      models.TypeMaintenance,
			IsDefault: true,
			IsActive:  true,
			SortOrder: 8,
			CreatedBy: &adminUser.ID,
		},
	}

	// 批量创建默认模板
	for _, template := range defaultTemplates {
		if err := db.Create(&template).Error; err != nil {
			return err
		}
	}

	return nil
}

// hashPassword 加密密码
func hashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return fmt.Sprintf("%x", hash)
}
