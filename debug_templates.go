package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	db, err := sql.Open("sqlite3", "./projectm2.db")
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	rows, err := db.Query("SELECT id, name, title, content FROM task_templates LIMIT 10")
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	fmt.Println("模板数据:")
	fmt.Println("ID | Name | Title | Content")
	fmt.Println("---|------|-------|--------")

	for rows.Next() {
		var id int
		var name, title, content string
		err := rows.Scan(&id, &name, &title, &content)
		if err != nil {
			log.Fatal(err)
		}

		// 截断内容显示
		contentPreview := content
		if len(content) > 50 {
			contentPreview = content[:50] + "..."
		}

		fmt.Printf("%d | %s | %s | %s\n", id, name, title, contentPreview)
	}
}
